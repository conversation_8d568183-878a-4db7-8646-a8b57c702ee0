<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { isNumber } from 'lodash-es';
import { deliverSkuApi, itemApi, skuApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { disableTreeItem, treeSelectTag } from '@/utils/common';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.DeliverSku | null;
  /** the meta options */
  metaOptions?: TreeOption[];
  /** the area options */
  areaOptions?: TreeOption[];
  /** deliver data */
  deliver: Api.Wms.Deliver | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const itemOptions = ref<TreeOption[]>([]);
const skuOptions = ref<TreeOption[]>([]);
const precision = ref<number>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<
  Api.Wms.DeliverSku,
  'deliverId' | 'metaId' | 'itemId' | 'skuId' | 'num' | 'areaNum' | 'pcs' | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    deliverId: props.deliver?.id || 0,
    metaId: null,
    itemId: null,
    skuId: null,
    num: 0,
    pcs: 1,
    areaNum: [],
    status: false
  };
}

type RuleKey = Extract<keyof Model, 'deliverId' | 'itemId' | 'skuId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  deliverId: defaultRequiredRule,
  itemId: defaultRequiredRule,
  skuId: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增规格',
    edit: '编辑规格'
  };
  return titles[props.operateType];
});

// 计算已分配总数量
const totalAreaNum = computed(() => {
  // 使用精度处理每个数值，避免浮点数累加精度误差
  const total = model.value.areaNum.reduce((acc, cur) => {
    const currentNum = Number((cur.num || 0).toFixed(precision.value));
    const accNum = Number(acc.toFixed(precision.value));
    return Number((accNum + currentNum).toFixed(precision.value));
  }, 0);
  return total;
});

// 检查分配数量是否超出出库量
const isAreaNumExceeded = computed(() => {
  // 使用精度处理后的数值进行比较，避免浮点数精度问题
  return totalAreaNum.value > Number((model.value.num || 0).toFixed(precision.value));
});

// 检查分配数量是否等于出库量
const isAreaNumMatched = computed(() => {
  return totalAreaNum.value === Number((model.value.num || 0).toFixed(precision.value));
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
    model.value.metaId = props.rowData?.item?.metaId || 0;
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit(fn?: () => void) {
  loading.value = true;
  try {
    // 验证分配数量不能超过出库量
    if (isAreaNumExceeded.value) {
      window.$message?.error('已分配的数量不得大于出库量');
      return;
    }
    model.value.areaNum = model.value.areaNum.filter(item => item.num > 0);

    // 验证是否完全分配
    if (!isAreaNumMatched.value) {
      window.$message?.error('出库量未完全分配');
      return;
    }

    // 验证库位是否已分配
    const areaIds = model.value.areaNum.map(item => item.areaId).filter(item => isNumber(item));
    if (areaIds.length !== model.value.areaNum.length) {
      window.$message?.error('库位不能为空');
      return;
    }

    // 执行额外函数
    if (fn) {
      fn();
    }

    await validate();
    // request
    const { error } =
      props.operateType === 'edit' ? await deliverSkuApi.save(model.value) : await deliverSkuApi.add(model.value);

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, async () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();

    await getItems();
    await getSkus();
    // 初始化精度
    precision.value = props.rowData?.sku?.precision || 0;
  } else {
    itemOptions.value = [];
    skuOptions.value = [];
    precision.value = undefined;
  }
});

async function getItems() {
  // 获取物料
  const { data, error } = await itemApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true,
    metaId: model.value.metaId
  });
  if (!error) {
    itemOptions.value = data.records;
  }
}

async function getSkus() {
  // 获取规格
  const { data, error } = await skuApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true,
    itemId: model.value.itemId
  });
  if (!error) {
    skuOptions.value = data.records;
  }
}

async function handleMetaChange(value: number) {
  model.value.metaId = value;
  model.value.itemId = null;
  model.value.skuId = null;
  itemOptions.value = [];
  skuOptions.value = [];
  await getItems();
}

async function handleItemChange(value: number) {
  model.value.itemId = value;
  model.value.skuId = null;
  skuOptions.value = [];
  await getSkus();
}

function handleSkuChange(value: number, option: Api.Wms.Sku) {
  precision.value = value ? option.precision : 0;
}

function handleAreaChange(value: number, area: Api.Wms.Area) {
  model.value.areaNum.map(item => {
    if (item.areaId === value) {
      item.areaPath = value ? [...area.parentPath, area.id] : [];
    }
    return item;
  });
}

function handleAddAreaNum() {
  model.value.areaNum.push({ areaId: null, areaPath: [], num: 0 });
}

function handleRemoveAreaNum(index: number) {
  model.value.areaNum.splice(index, 1);
}

const areaTree = computed(() => {
  const ids = model.value.areaNum.map(item => item.areaId || 0);
  return ids ? disableTreeItem(props.areaOptions, ids) : props.areaOptions;
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="600">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="出库单号" path="deliverId">
          <RemoteSelect
            v-model:value="model.deliverId"
            label-field="code"
            value-field="id"
            :options="[deliver]"
            :api-fn="async () => {}"
            placeholder="请选择出库单"
            disabled
          />
        </NFormItem>
        <NFormItem label="物料分类" path="metaId">
          <NTreeSelect
            v-model:value="model.metaId"
            :options="metaOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择物料分类"
            :disabled="deliver !== null && deliver?.status > 2"
            clearable
            @update:value="handleMetaChange"
          />
        </NFormItem>
        <NFormItem label="物料" path="itemId">
          <NTreeSelect
            v-model:value="model.itemId"
            :options="itemOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择物料"
            clearable
            :disabled="itemOptions.length === 0 || (deliver !== null && deliver?.status > 2)"
            @update:value="handleItemChange"
          />
        </NFormItem>
        <NFormItem label="规格" path="skuId">
          <NTreeSelect
            v-model:value="model.skuId"
            :options="skuOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择规格"
            clearable
            :disabled="skuOptions.length === 0 || (deliver !== null && deliver?.status > 2)"
            @update:value="handleSkuChange"
          />
        </NFormItem>
        <NFormItem label="包装规格" path="num">
          <NInputNumber
            v-model:value="model.pcs"
            :min="0"
            :precision="precision"
            placeholder="请输入包装规格"
            :disabled="deliver !== null && deliver?.status > 2"
            clearable
          >
            <template #suffix>
              <span>PCS</span>
            </template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="出库量" path="num">
          <NInputNumber
            v-model:value="model.num"
            :min="0"
            :precision="precision"
            placeholder="请输入数量"
            :disabled="deliver !== null && deliver?.status > 2"
            clearable
          >
            <template #suffix>
              <span>{{ rowData?.sku?.unit }}</span>
            </template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="库位分配" path="areaNum">
          <div class="flex flex-col gap-y-12px" :class="model.areaNum.length > 0 && 'w-full'">
            <div
              v-for="(item, index) in model.areaNum"
              :key="index"
              class="flex items-center justify-between gap-x-4px"
            >
              <NInputNumber
                v-model:value="item.num"
                class="flex-1"
                :min="0"
                :precision="precision"
                placeholder="请输入数量"
                :disabled="model.status || (deliver !== null && deliver?.status > 3)"
                clearable
              />
              <NTreeSelect
                v-model:value="item.areaId"
                class="flex-1"
                :options="areaTree"
                key-field="id"
                label-field="name"
                :render-tag="({ option }) => treeSelectTag(option, areaOptions, 'name')"
                placeholder="请选择库位"
                :disabled="model.status || (deliver !== null && deliver?.status > 3)"
                clearable
                @update:value="handleAreaChange"
              />
              <NButton :disabled="model.status" @click="handleRemoveAreaNum(index)">
                <template #icon>
                  <icon-ph-trash class="text-red" />
                </template>
              </NButton>
            </div>
            <div class="flex items-center gap-x-12px">
              <NButton dashed :disabled="isAreaNumExceeded || isAreaNumMatched" @click="handleAddAreaNum">
                <template #icon>
                  <icon-ph-plus />
                </template>
                添加
              </NButton>
              <div v-if="model.areaNum.length > 0" class="flex items-center gap-x-4px text-13px">
                <span>已分配</span>
                <span class="text-14px font-bold" :class="{ 'text-red': isAreaNumExceeded }">
                  {{ precision !== undefined ? totalAreaNum : 0 }}
                </span>
                <span>{{ rowData?.sku?.unit }}</span>
                <span v-if="isAreaNumExceeded" class="ml-8px text-12px text-red">超出出库量</span>
              </div>
            </div>
          </div>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton
            v-if="!model.status && deliver?.status === 3"
            type="primary"
            ghost
            :loading="loading"
            @click="handleSubmit(() => (model.status = true))"
          >
            确认出库
          </NButton>
          <NButton v-if="!model.status" type="primary" :loading="loading" @click="handleSubmit()">保存</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
